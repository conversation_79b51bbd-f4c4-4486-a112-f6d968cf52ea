// TODO: remove this file when it's ready to make the CollectionWidget component production-ready
import { components } from "@saf/sdk"

// Mock data for demonstration - in real implementation this would come from the data source
const generateMockData = (count: number = 5) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    title: `Item ${i + 1}`,
    subtitle: `This is the subtitle for item ${i + 1}`,
    meta: `Category ${(i % 3) + 1}`,
    image: `https://picsum.photos/200/200?random=${i + 1}`,
  }))
}

const CollectionItem = ({
  item,
  itemsData = {
    meta: "{{meta}}",
    image: "{{image}}",
    title: "{{title}}",
    subtitle: "{{subtitle}}",
  },
}: {
  item: any
  itemsData: components["schemas"]["CollectionWidget"]["config"]["itemsData"]
}) => {
  return (
    <div>
      {item.image && (
        <div>
          <img
            src={item.image}
            alt={item.title || "Collection item"}
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = "none"
            }}
          />
        </div>
      )}
      <div>
        {item.meta && <p>{item.meta}</p>}
        {item.title && <h4>{item.title}</h4>}
        {item.subtitle && <p>{item.subtitle}</p>}
      </div>
    </div>
  )
}

export const CollectionWidgetPoc = ({ data }: { data: components["schemas"]["CollectionWidget"] }) => {
  const { config } = data
  const { itemsData, options, pagination } = config

  const mockItems = generateMockData(options?.limitItems || 5)

  if (!itemsData?.title && !itemsData?.subtitle && !itemsData?.meta && !itemsData?.image) {
    return <div>No collection configuration</div>
  }

  if (!mockItems || mockItems.length === 0) {
    return <div>No items to display</div>
  }

  return (
    <div>
      <div>
        {mockItems.map((item) => (
          <CollectionItem key={item.id} item={item} itemsData={itemsData} />
        ))}
      </div>

      {pagination?.enabled && (
        <div>
          {pagination.page || "1"} / {pagination.limit || "10"}
        </div>
      )}
    </div>
  )
}
